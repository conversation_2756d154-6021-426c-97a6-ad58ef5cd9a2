package com.yxt.order.types.utils;

import static com.yxt.order.types.utils.ShardingHelper.NOT_VIP_NO;
import static com.yxt.order.types.utils.ShardingHelper.NOT_VIP_NO_MIGRATION;
import static com.yxt.order.types.utils.ShardingHelper.VIP_NO;
import static com.yxt.order.types.utils.ShardingHelper.VIP_NO_MIGRATION;


import com.yxt.order.types.offline.OfflineUserId;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Set;
import org.springframework.util.Assert;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月09日 18:22
 * @email: <EMAIL>
 */
public class OfflineNoCreate {

  /**
   * 内部订单号规则: 会员非会员标识1位(1:会员订单 2:非会员订单) + 12位随机数 + 分表位置6位(覆盖非会员分表位置)
   *
   * @param id
   * @param userId 心云系统的userId
   * @return
   */
  public static String create(String id, OfflineUserId userId, Date date) {
    return innerCreate(id, userId, date, Boolean.FALSE);
  }

  public static String migrateCreate(String id, OfflineUserId userId, Date date) {
    return innerCreate(id, userId, date, Boolean.TRUE);
  }

  private static String innerCreate(String id, OfflineUserId userId, Date date, Boolean migrate) {
    Assert.isTrue(Objects.nonNull(id), "创建单号,id不能为空");

    String vipTag = Objects.nonNull(userId) ? VIP_NO : NOT_VIP_NO;
    if (migrate) { // 如果是迁移订单,则使用迁移前缀
      vipTag = Objects.nonNull(userId) ? VIP_NO_MIGRATION : NOT_VIP_NO_MIGRATION;
    }

    // 优化：使用增强的分布式ID生成策略，降低并发重复几率
    String distributeId = generateEnhancedDistributeId(id, userId, date);
    String tableIndex = ShardingHelper.calcTableIndex(vipTag, userId, date);

    // 生成单号
    String no = String.format("%s%s%s", vipTag, distributeId, tableIndex);
    ShardingHelper.validNoStr(no);
    return no;
  }

  /**
   * 生成增强的分布式ID，降低并发重复几率
   * 策略：
   * 1. 使用雪花算法ID的更多位数信息
   * 2. 结合当前纳秒时间戳
   * 3. 加入随机因子
   * 4. 使用哈希算法确保分布均匀
   *
   * @param id 雪花算法生成的ID
   * @param userId 用户ID
   * @param date 创建时间
   * @return 14位增强的分布式ID
   */
  private static String generateEnhancedDistributeId(String id, OfflineUserId userId, Date date) {
    try {
      // 1. 获取雪花算法ID的更多信息（取后16位而不是14位）
      String snowflakeIdSuffix = id.length() >= 16 ? id.substring(id.length() - 16) : id;

      // 2. 获取当前纳秒时间戳的后6位，增加时间维度的唯一性
      long nanoTime = System.nanoTime();
      String nanoSuffix = String.valueOf(nanoTime).substring(Math.max(0, String.valueOf(nanoTime).length() - 6));

      // 3. 生成随机数，增加随机性
      int randomNum = ThreadLocalRandom.current().nextInt(100000, 999999);

      // 4. 结合用户ID和时间戳，增加用户维度的唯一性
      String userIdStr = userId != null ? userId.toString() : "0";
      String timeStr = String.valueOf(date.getTime());

      // 5. 构造组合字符串
      String combined = snowflakeIdSuffix + nanoSuffix + randomNum + userIdStr + timeStr;

      // 6. 使用SHA-256哈希算法生成固定长度的哈希值
      MessageDigest digest = MessageDigest.getInstance("SHA-256");
      byte[] hashBytes = digest.digest(combined.getBytes(StandardCharsets.UTF_8));

      // 7. 将哈希值转换为数字字符串，取前14位
      StringBuilder sb = new StringBuilder();
      for (byte b : hashBytes) {
        sb.append(String.format("%02x", b & 0xff));
      }

      // 8. 转换为纯数字字符串（避免字母）
      String hexStr = sb.toString();
      StringBuilder numStr = new StringBuilder();
      for (char c : hexStr.toCharArray()) {
        if (Character.isDigit(c)) {
          numStr.append(c);
        } else {
          // 将字母转换为数字 (a=0, b=1, c=2, d=3, e=4, f=5)
          numStr.append((int)(c - 'a'));
        }
        if (numStr.length() >= 14) {
          break;
        }
      }

      // 9. 确保长度为14位，不足则补充
      while (numStr.length() < 14) {
        numStr.append(ThreadLocalRandom.current().nextInt(10));
      }

      return numStr.substring(0, 14);

    } catch (Exception e) {
      // 降级策略：如果增强算法失败，回退到原始方案但加入随机数
      String originalId = id.substring(Math.max(0, id.length() - 12));
      int randomSuffix = ThreadLocalRandom.current().nextInt(10, 99);
      return String.format("%s%02d", originalId, randomSuffix);
    }
  }


  /**
   * 创建小表单号 ,目前表分为64个实体表
   *
   * @param id     雪花算法 id
   * @param userId 用户id
   * @param date   创建单据时间
   * @return 订单号
   */
  public static String createSmallTableBusinessNo(String id, OfflineUserId userId, Date date) {
    Assert.isTrue(Objects.nonNull(id), "创建单号,id不能为空");

    String vipTag = Objects.nonNull(userId) ? VIP_NO : NOT_VIP_NO;
    // 同样使用增强的分布式ID生成策略
    String distributeId = generateEnhancedDistributeId(id, userId, date);
    String tableIndex = ShardingHelper.calcSmallTableIndex(vipTag, userId, date);
    // 生成单号
    String no = String.format("%s%s%s", vipTag, distributeId, tableIndex);
    ShardingHelper.validNoStr(no);
    return no;
  }


}
