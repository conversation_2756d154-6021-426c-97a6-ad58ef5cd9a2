package com.yxt.order.types.utils;

import static com.yxt.order.types.utils.ShardingHelper.NOT_VIP_NO;
import static com.yxt.order.types.utils.ShardingHelper.NOT_VIP_NO_MIGRATION;
import static com.yxt.order.types.utils.ShardingHelper.VIP_NO;
import static com.yxt.order.types.utils.ShardingHelper.VIP_NO_MIGRATION;


import com.yxt.order.types.offline.OfflineUserId;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import org.springframework.util.Assert;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月09日 18:22
 * @email: <EMAIL>
 */
public class OfflineNoCreate {

  /**
   * 内部订单号规则: 会员非会员标识1位(1:会员订单 2:非会员订单) + 12位随机数 + 分表位置6位(覆盖非会员分表位置)
   *
   * @param id
   * @param userId 心云系统的userId
   * @return
   */
  public static String create(String id, OfflineUserId userId, Date date) {
    return innerCreate(id, userId, date, Boolean.FALSE);
  }

  public static String migrateCreate(String id, OfflineUserId userId, Date date) {
    return innerCreate(id, userId, date, Boolean.TRUE);
  }

  private static String innerCreate(String id, OfflineUserId userId, Date date, Boolean migrate) {
    Assert.isTrue(Objects.nonNull(id), "创建单号,id不能为空");

    String vipTag = Objects.nonNull(userId) ? VIP_NO : NOT_VIP_NO;
    if (migrate) { // 如果是迁移订单,则使用迁移前缀
      vipTag = Objects.nonNull(userId) ? VIP_NO_MIGRATION : NOT_VIP_NO_MIGRATION;
    }
    String distributeId = id.substring(id.length() - 14);
    String tableIndex = ShardingHelper.calcTableIndex(vipTag, userId, date);
    // 生成单号
    String no = String.format("%s%s%s", vipTag, distributeId, tableIndex);
    ShardingHelper.validNoStr(no);
    return no;
  }


  /**
   * 创建小表单号 ,目前表分为64个实体表
   *
   * @param id     雪花算法 id
   * @param userId 用户id
   * @param date   创建单据时间
   * @return 订单号
   */
  public static String createSmallTableBusinessNo(String id, OfflineUserId userId, Date date) {
    Assert.isTrue(Objects.nonNull(id), "创建单号,id不能为空");

    String vipTag = Objects.nonNull(userId) ? VIP_NO : NOT_VIP_NO;
    String distributeId = id.substring(id.length() - 14);
    String tableIndex = ShardingHelper.calcSmallTableIndex(vipTag, userId, date);
    // 生成单号
    String no = String.format("%s%s%s", vipTag, distributeId, tableIndex);
    ShardingHelper.validNoStr(no);
    return no;
  }


}
