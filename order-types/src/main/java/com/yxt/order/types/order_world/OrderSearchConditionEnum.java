package com.yxt.order.types.order_world;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderSearchConditionEnum {

  STORE_CODE("线上门店编码,支持多个"),

  ORG_CODE("订单所属机构编码,支持多个"),

  ORDER_START_CREATED("开始时间(订单创建时间)"),

  ORDER_END_CREATED("结束时间(订单创建时间)"),

  ORDER_NO("订单号,支持多个"),

  THIRD_ORDER_NO("三方订单号,支持多个"),

  ERP_INFO("商品编码/商品名称"),

  PLATFORM("平台编码,支持多个"),

  ORDER_FLAG("订单标记"),

  ORDER_FLAG_AND("订单标记(多个，and查询)"),

  ORDER_FLAG_OR("订单标记(多个，or查询)"),

  ORDER_STATUS("订单状态,具体状态值见OrderMainStatus,支持多个"),

  ORDER_PAYMENT_STATUS("订单支付状态,具体状态值见OrderPaymentStatus,支持多个"),

  BUSINESS_TYPE("业务类型,O2O/B2C/JOIN_B2B"),

  TRANSACTION_CHANNEL("交易场景,online:线上交易 ,offline:线下交易"),

  USER_ID("会员id,支持多个"),

  LAUNCH_USER_ID("下单发起人id"),

  LAUNCHED_ORG_CODE("下单发起方机构编码"),

  COMPANY_CODE("子公司编码"),

  PAY_TYPE("支付方式"),

  ORDER_TYPE("订单类型"),

  INCLUDE_INVALID("查询是否需要包含无效订单，1：包含，否则只查有效订单"),

  ABNORMAL_TYPE("异常类型，见 OrderAbnormalType"),

  ORDER_TAG("订单标签"),
  ;

  private final String desc;
}
