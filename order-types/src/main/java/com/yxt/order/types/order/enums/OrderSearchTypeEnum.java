package com.yxt.order.types.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderSearchTypeEnum {

  ORDER_SOURCE("订单来源"),

  STORE_CODE("线上门店编码"),

  ORG_CODE("实际发货机构编码"),

  SOURCE_STORE_CODE("下单门店编码"),

  SOURCE_ORG_CODE("下单机构编码"),

  START_DATE("开始时间"),

  END_DATE("结束时间"),

  ORDER_NO("订单号"),

  THIRD_ORDER_NO("三方订单号"),

  ERP_INFO("商品编码/商品名称"),

  ERP_CODE_OR("商品编码(多个，or查询)"),

  ORG_REL("实际发货机构编码/下单机构编码"),

  PLATFORM("平台编码"),

  ORDER_FLAG("订单标记"),

  ORDER_FLAG_AND("订单标记(多个，and查询)"),

  ORDER_FLAG_OR("订单标记(多个，or查询)"),

  ORDER_STATUS("订单状态"),

  ERP_STATUS("下账状态"),

  DELIVERY_TYPE("配送方式"),

  SERVICE_MODE("服务模式,O2O/B2C/B2B"),
  ;

  private final String desc;
}
