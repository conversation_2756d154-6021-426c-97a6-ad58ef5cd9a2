package com.yxt.order.types.order_world;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单标签类型枚举
 */
@Getter
@AllArgsConstructor
public enum OrderTagTypeEnum {

  SYSTEM("系统标签"),

  MANUAL("人工标签"),;

  private final String desc;

  public static String getDes(String name){
    for (OrderTagTypeEnum orderTagTypeEnum : OrderTagTypeEnum.values()) {
      if(orderTagTypeEnum.name().equals(name)) {
        return orderTagTypeEnum.getDesc();
      }
    }
    return null;
  }

}
