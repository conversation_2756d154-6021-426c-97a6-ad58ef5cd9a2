package com.yxt.order.types.order_world;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 启停状态枚举
 */
@Getter
@AllArgsConstructor
public enum EnableDisabledStatusEnum {

  ENABLE("1", "启用"),
  DISABLED("0", "停用");

  private final String code;

  private final String desc;

  public static String getDes(String code){
    for (EnableDisabledStatusEnum enableDisabledStatusEnum : EnableDisabledStatusEnum.values()) {
      if(enableDisabledStatusEnum.getCode().equals(code)) {
        return enableDisabledStatusEnum.getDesc();
      }
    }
    return null;
  }
}
