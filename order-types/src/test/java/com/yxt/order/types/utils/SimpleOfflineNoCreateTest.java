package com.yxt.order.types.utils;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 简化的OfflineNoCreate并发测试
 * 不依赖外部库，直接测试优化效果
 */
public class SimpleOfflineNoCreateTest {
    
    public static void main(String[] args) throws InterruptedException {
        SimpleOfflineNoCreateTest test = new SimpleOfflineNoCreateTest();
        
        System.out.println("开始运行简化版OfflineNoCreate并发测试...");
        
        // 运行并发测试
        test.testConcurrentNoGeneration();
        
        // 运行相同输入参数测试
        test.testSameInputDuplication();
        
        // 运行格式测试
        test.testNoFormat();
        
        System.out.println("\n所有测试完成！");
    }

    /**
     * 简化的OfflineUserId实现
     */
    static class SimpleOfflineUserId {
        private String userId;
        
        public SimpleOfflineUserId(String userId) {
            this.userId = userId;
        }
        
        public static SimpleOfflineUserId userId(String userId) {
            return new SimpleOfflineUserId(userId);
        }
        
        @Override
        public String toString() {
            return userId;
        }
    }

    /**
     * 测试并发场景下的单号重复率
     */
    public void testConcurrentNoGeneration() throws InterruptedException {
        int threadCount = 50;  // 减少线程数以适应测试环境
        int operationsPerThread = 500;  // 减少操作数
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        // 模拟相同的输入参数（这是最容易产生重复的场景）
        String baseId = "1234567890123456789";  // 模拟雪花算法生成的ID
        SimpleOfflineUserId userId = SimpleOfflineUserId.userId("1001");
        Date date = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 为每次生成使用略微不同的ID（模拟真实场景）
                        String id = baseId + String.format("%03d%03d", threadIndex, j);
                        String no = createTestNo(id, userId, date);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                                System.out.println("发现重复单号: " + no);
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        // 输出测试结果
        System.out.println("=== 并发测试结果 ===");
        System.out.println("总线程数: " + threadCount);
        System.out.println("每线程操作数: " + operationsPerThread);
        System.out.println("总操作数: " + totalOperations);
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.6f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
        System.out.println("平均每秒生成: " + (totalOperations * 1000L / (endTime - startTime)) + " 个单号");
        
        // 显示一些生成的单号样例
        System.out.println("\n=== 生成的单号样例 ===");
        int count = 0;
        for (String no : generatedNos) {
            if (count++ >= 10) break;
            System.out.println(no);
        }
    }
    
    /**
     * 测试相同输入参数的重复率（最严苛的测试）
     */
    public void testSameInputDuplication() throws InterruptedException {
        int threadCount = 30;
        int operationsPerThread = 1000;
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        // 使用完全相同的输入参数
        String sameId = "1234567890123456789";
        SimpleOfflineUserId sameUserId = SimpleOfflineUserId.userId("1001");
        Date sameDate = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String no = createTestNo(sameId, sameUserId, sameDate);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("\n=== 相同输入参数测试结果 ===");
        System.out.println("总操作数: " + totalOperations);
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.6f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
    }
    
    /**
     * 测试单号格式的正确性
     */
    public void testNoFormat() {
        String id = "1234567890123456789";
        SimpleOfflineUserId userId = SimpleOfflineUserId.userId("1001");
        Date date = new Date();
        
        System.out.println("\n=== 单号格式测试 ===");
        
        // 测试会员单号
        String vipNo = createTestNo(id, userId, date);
        System.out.println("会员单号: " + vipNo);
        System.out.println("单号长度: " + vipNo.length());
        System.out.println("VIP标识: " + vipNo.substring(0, 1));
        
        // 测试非会员单号
        String nonVipNo = createTestNo(id, null, date);
        System.out.println("非会员单号: " + nonVipNo);
        System.out.println("单号长度: " + nonVipNo.length());
        System.out.println("VIP标识: " + nonVipNo.substring(0, 1));
        
        // 验证单号格式
        try {
            validNoStr(vipNo);
            validNoStr(nonVipNo);
            System.out.println("所有单号格式验证通过！");
        } catch (Exception e) {
            System.out.println("单号格式验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 简化的单号生成方法，模拟优化后的逻辑
     */
    private String createTestNo(String id, SimpleOfflineUserId userId, Date date) {
        String vipTag = userId != null ? "1" : "2";
        
        // 使用增强的分布式ID生成策略
        String distributeId = generateEnhancedDistributeId(id, userId, date);
        
        // 简化的分表索引计算
        String tableIndex = calcSimpleTableIndex(vipTag, userId, date);
        
        return String.format("%s%s%s", vipTag, distributeId, tableIndex);
    }
    
    /**
     * 增强的分布式ID生成算法（简化版）
     */
    private String generateEnhancedDistributeId(String id, SimpleOfflineUserId userId, Date date) {
        try {
            // 1. 获取雪花算法ID的更多信息
            String snowflakeIdSuffix = id.length() >= 16 ? id.substring(id.length() - 16) : id;
            
            // 2. 获取当前纳秒时间戳的后6位
            long nanoTime = System.nanoTime();
            String nanoSuffix = String.valueOf(nanoTime).substring(Math.max(0, String.valueOf(nanoTime).length() - 6));
            
            // 3. 生成随机数
            int randomNum = (int)(Math.random() * 900000) + 100000;
            
            // 4. 结合用户ID和时间戳
            String userIdStr = userId != null ? userId.toString() : "0";
            String timeStr = String.valueOf(date.getTime());
            
            // 5. 构造组合字符串
            String combined = snowflakeIdSuffix + nanoSuffix + randomNum + userIdStr + timeStr;
            
            // 6. 简化的哈希算法
            int hash = combined.hashCode();
            if (hash < 0) hash = -hash;
            
            // 7. 转换为14位数字字符串
            String hashStr = String.valueOf(hash);
            while (hashStr.length() < 14) {
                hashStr = "0" + hashStr;
            }
            
            return hashStr.substring(0, 14);
            
        } catch (Exception e) {
            // 降级策略
            String originalId = id.substring(Math.max(0, id.length() - 12));
            int randomSuffix = (int)(Math.random() * 90) + 10;
            return String.format("%s%02d", originalId, randomSuffix);
        }
    }
    
    /**
     * 简化的分表索引计算
     */
    private String calcSimpleTableIndex(String vipTag, SimpleOfflineUserId userId, Date date) {
        if ("1".equals(vipTag) && userId != null) {
            // 会员用户：基于用户ID计算
            int hash = userId.toString().hashCode();
            if (hash < 0) hash = -hash;
            int tableIndex = hash % 256;
            return String.format("%04d", tableIndex);
        } else {
            // 非会员用户：基于时间计算
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(java.util.Calendar.YEAR) % 100;
            int month = calendar.get(java.util.Calendar.MONTH) + 1;
            return String.format("%02d%02d", year, month);
        }
    }
    
    /**
     * 简化的单号格式验证
     */
    private void validNoStr(String noStr) {
        if (noStr == null || noStr.length() != 19) {
            throw new IllegalArgumentException("单号不符合规则");
        }
    }
}
