package com.yxt.order.types.utils;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 真正的零重复算法测试
 * 使用更强的唯一性保证机制
 */
public class TrueZeroDuplicateTest {
    
    public static void main(String[] args) throws InterruptedException {
        TrueZeroDuplicateTest test = new TrueZeroDuplicateTest();
        
        System.out.println("=== 真正零重复算法验证测试 ===");
        System.out.println();
        
        // 极限并发测试
        test.extremeConcurrencyTest();
        
        // 百万级测试
        test.millionScaleTest();
        
        System.out.println("\n=== 真正零重复测试完成 ===");
    }

    /**
     * 简化的OfflineUserId实现
     */
    static class SimpleOfflineUserId {
        private String userId;
        
        public SimpleOfflineUserId(String userId) {
            this.userId = userId;
        }
        
        public static SimpleOfflineUserId userId(String userId) {
            return new SimpleOfflineUserId(userId);
        }
        
        @Override
        public String toString() {
            return userId;
        }
    }

    /**
     * 极限并发测试：完全相同输入参数
     */
    public void extremeConcurrencyTest() throws InterruptedException {
        System.out.println("1. 极限并发测试（完全相同输入参数）");
        System.out.println("=====================================");
        
        int threadCount = 500;  // 更多线程
        int operationsPerThread = 1000;
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        // 使用完全相同的输入参数
        String sameId = "1234567890123456789";
        SimpleOfflineUserId sameUserId = SimpleOfflineUserId.userId("1001");
        Date sameDate = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String no = createTrueZeroDuplicateNo(sameId, sameUserId, sameDate);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                                System.out.println("发现重复单号: " + no);
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("测试配置: " + threadCount + " 线程 × " + operationsPerThread + " 操作");
        System.out.println("总操作数: " + totalOperations);
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.8f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
        System.out.println("TPS: " + (totalOperations * 1000L / (endTime - startTime)));
        
        if (duplicateCount.get() == 0) {
            System.out.println("✅ 真正零重复测试通过！");
        } else {
            System.out.println("❌ 仍有重复，需要进一步优化");
        }
        System.out.println();
    }
    
    /**
     * 百万级测试
     */
    public void millionScaleTest() throws InterruptedException {
        System.out.println("2. 百万级真正零重复测试");
        System.out.println("========================");
        
        int threadCount = 100;
        int operationsPerThread = 10000;  // 总计100万次操作
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        String baseId = "9999999999999999999";
        SimpleOfflineUserId userId = SimpleOfflineUserId.userId("9999");
        Date date = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 使用略微不同的ID
                        String id = baseId + String.format("%03d%05d", threadIndex, j);
                        String no = createTrueZeroDuplicateNo(id, userId, date);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("测试配置: " + threadCount + " 线程 × " + operationsPerThread + " 操作");
        System.out.println("总操作数: " + totalOperations + " (100万)");
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.8f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
        System.out.println("平均TPS: " + (totalOperations * 1000L / (endTime - startTime)));
        
        if (duplicateCount.get() == 0) {
            System.out.println("✅ 百万级真正零重复测试通过！");
        } else {
            System.out.println("❌ 仍有重复，重复率: " + String.format("%.8f%%", (double) duplicateCount.get() / totalOperations * 100));
        }
    }
    
    /**
     * 真正零重复的单号生成器
     * 使用全局锁和严格的唯一性检查
     */
    private static final AtomicLong GLOBAL_COUNTER = new AtomicLong(0);
    private static final ReentrantLock GENERATION_LOCK = new ReentrantLock();
    private static final Set<String> GLOBAL_UNIQUE_SET = new HashSet<>();
    
    private String createTrueZeroDuplicateNo(String id, SimpleOfflineUserId userId, Date date) {
        String vipTag = userId != null ? "1" : "2";
        
        // 使用真正零重复的算法
        String distributeId = generateTrueZeroDuplicateId(id, userId, date);
        
        // 简化的分表索引计算
        String tableIndex = calcTableIndex(vipTag, userId, date);
        
        return String.format("%s%s%s", vipTag, distributeId, tableIndex);
    }
    
    /**
     * 真正零重复的ID生成算法
     * 核心思想：使用全局锁确保原子性，结合全局计数器确保唯一性
     */
    private String generateTrueZeroDuplicateId(String id, SimpleOfflineUserId userId, Date date) {
        GENERATION_LOCK.lock();
        try {
            String candidateId;
            int attempt = 0;
            
            do {
                // 1. 获取全局唯一计数器
                long globalCounter = GLOBAL_COUNTER.incrementAndGet();
                
                // 2. 获取高精度时间戳
                long nanoTime = System.nanoTime();
                
                // 3. 获取线程ID
                long threadId = Thread.currentThread().getId();
                
                // 4. 生成随机数
                int randomNum = (int)(Math.random() * 900000) + 100000;
                
                // 5. 结合所有信息
                String userIdStr = userId != null ? userId.toString() : "0";
                String timeStr = String.valueOf(date.getTime());
                
                // 6. 构造唯一字符串（包含全局计数器确保唯一性）
                String uniqueStr = String.format("%s_%d_%d_%d_%d_%s_%s_%d", 
                    id, globalCounter, nanoTime, threadId, randomNum, userIdStr, timeStr, attempt);
                
                // 7. 生成哈希并转换为14位数字
                int hash = uniqueStr.hashCode();
                if (hash < 0) hash = -hash;
                
                // 8. 确保14位长度，前面补0
                candidateId = String.format("%014d", hash % 100000000000000L);
                
                // 9. 如果仍然重复（理论上不可能），增加尝试次数
                attempt++;
                
                // 10. 防止无限循环
                if (attempt > 100) {
                    // 使用全局计数器作为后备方案
                    candidateId = String.format("%014d", globalCounter % 100000000000000L);
                    break;
                }
                
            } while (GLOBAL_UNIQUE_SET.contains(candidateId));
            
            // 11. 记录已生成的ID
            GLOBAL_UNIQUE_SET.add(candidateId);
            
            // 12. 定期清理缓存（保留最近的100万个）
            if (GLOBAL_UNIQUE_SET.size() > 1000000) {
                // 简单清理：清空一半
                Set<String> newSet = new HashSet<>();
                int count = 0;
                for (String existingId : GLOBAL_UNIQUE_SET) {
                    if (count++ > 500000) {
                        newSet.add(existingId);
                    }
                }
                GLOBAL_UNIQUE_SET.clear();
                GLOBAL_UNIQUE_SET.addAll(newSet);
            }
            
            return candidateId;
            
        } finally {
            GENERATION_LOCK.unlock();
        }
    }
    
    /**
     * 简化的分表索引计算
     */
    private String calcTableIndex(String vipTag, SimpleOfflineUserId userId, Date date) {
        if ("1".equals(vipTag) && userId != null) {
            int hash = userId.toString().hashCode();
            if (hash < 0) hash = -hash;
            int tableIndex = hash % 256;
            return String.format("%04d", tableIndex);
        } else {
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(java.util.Calendar.YEAR) % 100;
            int month = calendar.get(java.util.Calendar.MONTH) + 1;
            return String.format("%02d%02d", year, month);
        }
    }
}
