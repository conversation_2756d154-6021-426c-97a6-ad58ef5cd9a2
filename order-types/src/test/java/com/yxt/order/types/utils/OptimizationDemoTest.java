package com.yxt.order.types.utils;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 优化效果演示测试
 * 展示优化前后的对比效果
 */
public class OptimizationDemoTest {
    
    public static void main(String[] args) throws InterruptedException {
        OptimizationDemoTest test = new OptimizationDemoTest();
        
        System.out.println("=== OfflineNoCreate 优化效果演示 ===");
        System.out.println();
        
        // 演示单号生成
        test.demonstrateNoGeneration();
        
        // 演示并发性能
        test.demonstrateConcurrentPerformance();
        
        System.out.println("\n=== 演示完成 ===");
    }

    /**
     * 演示单号生成效果
     */
    public void demonstrateNoGeneration() {
        System.out.println("1. 单号生成演示");
        System.out.println("================");
        
        String id = "1234567890123456789";
        Date date = new Date();
        
        // 生成多个单号展示唯一性
        Set<String> generatedNos = new HashSet<>();
        
        System.out.println("使用相同输入参数生成10个单号：");
        for (int i = 0; i < 10; i++) {
            String no = generateOptimizedNo(id, "1001", date);
            generatedNos.add(no);
            System.out.println("单号 " + (i+1) + ": " + no);
        }
        
        System.out.println("\n生成的唯一单号数量: " + generatedNos.size() + "/10");
        System.out.println("重复率: " + String.format("%.2f%%", (10 - generatedNos.size()) * 10.0));
        
        // 验证单号格式
        String sampleNo = generatedNos.iterator().next();
        System.out.println("\n单号格式验证:");
        System.out.println("样例单号: " + sampleNo);
        System.out.println("单号长度: " + sampleNo.length());
        System.out.println("VIP标识: " + sampleNo.substring(0, 1));
        System.out.println("分布式ID: " + sampleNo.substring(1, 15));
        System.out.println("分表索引: " + sampleNo.substring(15, 19));
        System.out.println();
    }
    
    /**
     * 演示并发性能
     */
    public void demonstrateConcurrentPerformance() throws InterruptedException {
        System.out.println("2. 并发性能演示");
        System.out.println("================");
        
        int[] threadCounts = {10, 50, 100};
        int operationsPerThread = 1000;
        
        for (int threadCount : threadCounts) {
            System.out.println("\n测试配置: " + threadCount + " 线程 × " + operationsPerThread + " 操作");
            
            long startTime = System.currentTimeMillis();
            TestResult result = runConcurrentTest(threadCount, operationsPerThread);
            long endTime = System.currentTimeMillis();
            
            System.out.println("总操作数: " + (threadCount * operationsPerThread));
            System.out.println("唯一单号数: " + result.uniqueCount);
            System.out.println("重复单号数: " + result.duplicateCount);
            System.out.println("重复率: " + String.format("%.6f%%", result.duplicateRate));
            System.out.println("执行时间: " + (endTime - startTime) + "ms");
            System.out.println("TPS: " + result.tps);
        }
    }
    
    /**
     * 运行并发测试
     */
    private TestResult runConcurrentTest(int threadCount, int operationsPerThread) throws InterruptedException {
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        String baseId = "1234567890123456789";
        String userId = "1001";
        Date date = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 使用略微不同的ID模拟真实场景
                        String id = baseId + String.format("%03d%03d", threadIndex, j);
                        String no = generateOptimizedNo(id, userId, date);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        double duplicateRate = (double) duplicateCount.get() / totalOperations * 100;
        long tps = totalOperations * 1000L / executionTime;
        
        return new TestResult(generatedNos.size(), duplicateCount.get(), duplicateRate, tps);
    }
    
    /**
     * 生成优化后的单号（简化版实现）
     */
    private String generateOptimizedNo(String id, String userId, Date date) {
        String vipTag = userId != null ? "1" : "2";
        
        // 使用增强的分布式ID生成策略
        String distributeId = generateEnhancedDistributeId(id, userId, date);
        
        // 简化的分表索引计算
        String tableIndex = calcTableIndex(vipTag, userId, date);
        
        return String.format("%s%s%s", vipTag, distributeId, tableIndex);
    }
    
    /**
     * 增强的分布式ID生成算法（演示版）
     */
    private String generateEnhancedDistributeId(String id, String userId, Date date) {
        try {
            // 1. 获取雪花算法ID的更多信息
            String snowflakeIdSuffix = id.length() >= 16 ? id.substring(id.length() - 16) : id;
            
            // 2. 获取当前纳秒时间戳
            long nanoTime = System.nanoTime();
            String nanoSuffix = String.valueOf(nanoTime).substring(Math.max(0, String.valueOf(nanoTime).length() - 6));
            
            // 3. 生成随机数
            int randomNum = (int)(Math.random() * 900000) + 100000;
            
            // 4. 结合用户ID和时间戳
            String userIdStr = userId != null ? userId : "0";
            String timeStr = String.valueOf(date.getTime());
            
            // 5. 构造组合字符串
            String combined = snowflakeIdSuffix + nanoSuffix + randomNum + userIdStr + timeStr;
            
            // 6. 使用哈希算法生成固定长度的哈希值
            int hash = combined.hashCode();
            if (hash < 0) hash = -hash;
            
            // 7. 转换为14位数字字符串
            String hashStr = String.valueOf(hash);
            while (hashStr.length() < 14) {
                hashStr = "0" + hashStr;
            }
            
            return hashStr.substring(0, 14);
            
        } catch (Exception e) {
            // 降级策略
            String originalId = id.substring(Math.max(0, id.length() - 12));
            int randomSuffix = (int)(Math.random() * 90) + 10;
            return String.format("%s%02d", originalId, randomSuffix);
        }
    }
    
    /**
     * 计算分表索引
     */
    private String calcTableIndex(String vipTag, String userId, Date date) {
        if ("1".equals(vipTag) && userId != null) {
            // 会员用户：基于用户ID计算
            int hash = userId.hashCode();
            if (hash < 0) hash = -hash;
            int tableIndex = hash % 256;
            return String.format("%04d", tableIndex);
        } else {
            // 非会员用户：基于时间计算
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(java.util.Calendar.YEAR) % 100;
            int month = calendar.get(java.util.Calendar.MONTH) + 1;
            return String.format("%02d%02d", year, month);
        }
    }
    
    /**
     * 测试结果封装类
     */
    private static class TestResult {
        final int uniqueCount;
        final int duplicateCount;
        final double duplicateRate;
        final long tps;
        
        TestResult(int uniqueCount, int duplicateCount, double duplicateRate, long tps) {
            this.uniqueCount = uniqueCount;
            this.duplicateCount = duplicateCount;
            this.duplicateRate = duplicateRate;
            this.tps = tps;
        }
    }
}
