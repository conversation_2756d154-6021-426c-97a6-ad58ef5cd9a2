package com.yxt.order.types.utils;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 零重复算法测试
 * 验证新的零重复算法是否真正实现了零重复
 */
public class ZeroDuplicateTest {
    
    public static void main(String[] args) throws InterruptedException {
        ZeroDuplicateTest test = new ZeroDuplicateTest();
        
        System.out.println("=== 零重复算法验证测试 ===");
        System.out.println();
        
        // 极限并发测试
        test.extremeConcurrencyTest();
        
        // 相同输入参数测试
        test.sameInputTest();
        
        // 大规模测试
        test.largScaleTest();
        
        System.out.println("\n=== 零重复测试完成 ===");
    }

    /**
     * 简化的OfflineUserId实现
     */
    static class SimpleOfflineUserId {
        private String userId;
        
        public SimpleOfflineUserId(String userId) {
            this.userId = userId;
        }
        
        public static SimpleOfflineUserId userId(String userId) {
            return new SimpleOfflineUserId(userId);
        }
        
        @Override
        public String toString() {
            return userId;
        }
    }

    /**
     * 极限并发测试：相同输入参数
     */
    public void extremeConcurrencyTest() throws InterruptedException {
        System.out.println("1. 极限并发测试（完全相同输入参数）");
        System.out.println("=====================================");
        
        int threadCount = 200;  // 增加线程数
        int operationsPerThread = 1000;  // 每线程操作数
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        // 使用完全相同的输入参数（最严苛的测试）
        String sameId = "1234567890123456789";
        SimpleOfflineUserId sameUserId = SimpleOfflineUserId.userId("1001");
        Date sameDate = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String no = createTestNo(sameId, sameUserId, sameDate);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                                System.out.println("发现重复单号: " + no);
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("测试配置: " + threadCount + " 线程 × " + operationsPerThread + " 操作");
        System.out.println("总操作数: " + totalOperations);
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.8f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
        System.out.println("TPS: " + (totalOperations * 1000L / (endTime - startTime)));
        
        if (duplicateCount.get() == 0) {
            System.out.println("✅ 零重复测试通过！");
        } else {
            System.out.println("❌ 仍有重复，需要进一步优化");
        }
        System.out.println();
    }
    
    /**
     * 相同输入参数测试（更大规模）
     */
    public void sameInputTest() throws InterruptedException {
        System.out.println("2. 大规模相同输入测试");
        System.out.println("====================");
        
        int threadCount = 100;
        int operationsPerThread = 5000;  // 增加操作数
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        String sameId = "9876543210987654321";
        SimpleOfflineUserId sameUserId = SimpleOfflineUserId.userId("2002");
        Date sameDate = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String no = createTestNo(sameId, sameUserId, sameDate);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("测试配置: " + threadCount + " 线程 × " + operationsPerThread + " 操作");
        System.out.println("总操作数: " + totalOperations);
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.8f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
        
        if (duplicateCount.get() == 0) {
            System.out.println("✅ 大规模零重复测试通过！");
        } else {
            System.out.println("❌ 仍有重复，需要进一步优化");
        }
        System.out.println();
    }
    
    /**
     * 大规模测试
     */
    public void largScaleTest() throws InterruptedException {
        System.out.println("3. 百万级大规模测试");
        System.out.println("==================");
        
        int threadCount = 50;
        int operationsPerThread = 20000;  // 总计100万次操作
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        String baseId = "1111111111111111111";
        SimpleOfflineUserId userId = SimpleOfflineUserId.userId("3003");
        Date date = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 使用略微不同的ID
                        String id = baseId + String.format("%03d%05d", threadIndex, j);
                        String no = createTestNo(id, userId, date);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("测试配置: " + threadCount + " 线程 × " + operationsPerThread + " 操作");
        System.out.println("总操作数: " + totalOperations + " (100万+)");
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.8f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
        System.out.println("平均TPS: " + (totalOperations * 1000L / (endTime - startTime)));
        
        if (duplicateCount.get() == 0) {
            System.out.println("✅ 百万级零重复测试通过！");
        } else {
            System.out.println("❌ 仍有重复，重复率: " + String.format("%.8f%%", (double) duplicateCount.get() / totalOperations * 100));
        }
    }
    
    /**
     * 模拟单号生成（简化版，用于测试）
     */
    private String createTestNo(String id, SimpleOfflineUserId userId, Date date) {
        String vipTag = userId != null ? "1" : "2";
        
        // 使用简化的零重复算法模拟
        String distributeId = generateSimpleZeroDuplicateId(id, userId, date);
        
        // 简化的分表索引计算
        String tableIndex = calcTableIndex(vipTag, userId, date);
        
        return String.format("%s%s%s", vipTag, distributeId, tableIndex);
    }
    
    /**
     * 简化的零重复ID生成（用于测试）
     */
    private static final java.util.concurrent.atomic.AtomicLong TEST_SEQUENCE = new java.util.concurrent.atomic.AtomicLong(0);
    private static final java.util.concurrent.ConcurrentHashMap<String, Long> TEST_CACHE = new java.util.concurrent.ConcurrentHashMap<>();
    
    private String generateSimpleZeroDuplicateId(String id, SimpleOfflineUserId userId, Date date) {
        for (int attempt = 0; attempt < 5; attempt++) {
            // 生成候选ID
            long globalSeq = TEST_SEQUENCE.incrementAndGet();
            long nanoTime = System.nanoTime();
            int randomNum = (int)(Math.random() * 900000) + 100000 + attempt * 1000;
            
            String userIdStr = userId != null ? userId.toString() : "0";
            String timeStr = String.valueOf(date.getTime());
            String combined = id + globalSeq + nanoTime + randomNum + userIdStr + timeStr + attempt;
            
            // 简化的哈希
            int hash = combined.hashCode();
            if (hash < 0) hash = -hash;
            String candidateId = String.format("%014d", hash % 100000000000000L);
            
            // 检查唯一性
            if (!TEST_CACHE.containsKey(candidateId)) {
                TEST_CACHE.put(candidateId, System.currentTimeMillis());
                
                // 简单的缓存清理
                if (TEST_CACHE.size() > 50000) {
                    TEST_CACHE.clear();
                }
                
                return candidateId;
            }
        }
        
        // 降级策略
        long globalSeq = TEST_SEQUENCE.incrementAndGet();
        return String.format("%014d", globalSeq % 100000000000000L);
    }
    
    /**
     * 简化的分表索引计算
     */
    private String calcTableIndex(String vipTag, SimpleOfflineUserId userId, Date date) {
        if ("1".equals(vipTag) && userId != null) {
            int hash = userId.toString().hashCode();
            if (hash < 0) hash = -hash;
            int tableIndex = hash % 256;
            return String.format("%04d", tableIndex);
        } else {
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(java.util.Calendar.YEAR) % 100;
            int month = calendar.get(java.util.Calendar.MONTH) + 1;
            return String.format("%02d%02d", year, month);
        }
    }
}
