package com.yxt.order.types.utils;

import com.yxt.order.types.offline.OfflineUserId;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 性能对比测试：优化前后的单号生成算法对比
 */
public class PerformanceComparisonTest {

    public static void main(String[] args) throws InterruptedException {
        PerformanceComparisonTest test = new PerformanceComparisonTest();
        
        System.out.println("开始运行性能对比测试...");
        
        // 对比测试
        test.compareOriginalVsOptimized();
        
        // 极限并发测试
        test.extremeConcurrencyTest();
        
        System.out.println("\n性能对比测试完成！");
    }

    /**
     * 模拟原始的单号生成方法（优化前）
     */
    private static String originalInnerCreate(String id, OfflineUserId userId, Date date) {
        String vipTag = userId != null ? "1" : "2";
        String distributeId = id.substring(id.length() - 14);  // 原始方法：直接截取后14位
        String tableIndex = ShardingHelper.calcTableIndex(vipTag, userId, date);
        return String.format("%s%s%s", vipTag, distributeId, tableIndex);
    }

    /**
     * 对比测试：原始方法 vs 优化方法
     */
    public void compareOriginalVsOptimized() throws InterruptedException {
        int threadCount = 50;
        int operationsPerThread = 1000;
        
        System.out.println("=== 性能对比测试 ===");
        System.out.println("线程数: " + threadCount);
        System.out.println("每线程操作数: " + operationsPerThread);
        System.out.println("总操作数: " + (threadCount * operationsPerThread));
        
        // 测试原始方法
        System.out.println("\n--- 测试原始方法 ---");
        TestResult originalResult = testMethod(threadCount, operationsPerThread, true);
        
        // 测试优化方法
        System.out.println("\n--- 测试优化方法 ---");
        TestResult optimizedResult = testMethod(threadCount, operationsPerThread, false);
        
        // 对比结果
        System.out.println("\n=== 对比结果 ===");
        System.out.println("原始方法重复率: " + String.format("%.6f%%", originalResult.duplicateRate));
        System.out.println("优化方法重复率: " + String.format("%.6f%%", optimizedResult.duplicateRate));
        
        if (optimizedResult.duplicateRate > 0) {
            System.out.println("重复率改善: " + String.format("%.2fx", originalResult.duplicateRate / optimizedResult.duplicateRate));
        } else {
            System.out.println("优化方法实现了零重复！");
        }
        
        System.out.println("原始方法执行时间: " + originalResult.executionTime + "ms");
        System.out.println("优化方法执行时间: " + optimizedResult.executionTime + "ms");
        System.out.println("性能影响: " + String.format("%.2fx", (double)optimizedResult.executionTime / originalResult.executionTime));
        
        System.out.println("原始方法TPS: " + originalResult.tps);
        System.out.println("优化方法TPS: " + optimizedResult.tps);
    }
    
    /**
     * 测试指定方法的性能和重复率
     */
    private TestResult testMethod(int threadCount, int operationsPerThread, boolean useOriginal) throws InterruptedException {
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        String baseId = "1234567890123456789";
        OfflineUserId userId = OfflineUserId.userId("1001");
        Date date = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String id = baseId + String.format("%03d%03d", threadIndex, j);
                        String no;
                        
                        if (useOriginal) {
                            no = originalInnerCreate(id, userId, date);
                        } else {
                            no = OfflineNoCreate.create(id, userId, date);
                        }
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        double duplicateRate = (double) duplicateCount.get() / totalOperations * 100;
        long tps = totalOperations * 1000L / executionTime;
        
        System.out.println("唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.6f%%", duplicateRate));
        System.out.println("执行时间: " + executionTime + "ms");
        System.out.println("TPS: " + tps);
        
        return new TestResult(duplicateRate, executionTime, tps);
    }
    
    /**
     * 极限并发测试：相同输入参数
     */
    public void extremeConcurrencyTest() throws InterruptedException {
        int threadCount = 100;
        int operationsPerThread = 500;
        
        System.out.println("\n=== 极限并发测试（相同输入参数） ===");
        
        // 原始方法测试
        System.out.println("\n--- 原始方法 ---");
        TestResult originalResult = testSameInputMethod(threadCount, operationsPerThread, true);
        
        // 优化方法测试
        System.out.println("\n--- 优化方法 ---");
        TestResult optimizedResult = testSameInputMethod(threadCount, operationsPerThread, false);
        
        System.out.println("\n=== 极限测试对比 ===");
        System.out.println("原始方法重复率: " + String.format("%.6f%%", originalResult.duplicateRate));
        System.out.println("优化方法重复率: " + String.format("%.6f%%", optimizedResult.duplicateRate));
        
        if (optimizedResult.duplicateRate > 0) {
            System.out.println("重复率改善: " + String.format("%.2fx", originalResult.duplicateRate / optimizedResult.duplicateRate));
        } else {
            System.out.println("优化方法实现了零重复！");
        }
    }
    
    private TestResult testSameInputMethod(int threadCount, int operationsPerThread, boolean useOriginal) throws InterruptedException {
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        // 使用完全相同的输入参数
        String sameId = "1234567890123456789";
        OfflineUserId sameUserId = OfflineUserId.userId("1001");
        Date sameDate = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String no;
                        
                        if (useOriginal) {
                            no = originalInnerCreate(sameId, sameUserId, sameDate);
                        } else {
                            no = OfflineNoCreate.create(sameId, sameUserId, sameDate);
                        }
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        double duplicateRate = (double) duplicateCount.get() / totalOperations * 100;
        long tps = totalOperations * 1000L / executionTime;
        
        System.out.println("总操作数: " + totalOperations);
        System.out.println("唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.6f%%", duplicateRate));
        System.out.println("执行时间: " + executionTime + "ms");
        
        return new TestResult(duplicateRate, executionTime, tps);
    }
    
    /**
     * 测试结果封装类
     */
    private static class TestResult {
        final double duplicateRate;
        final long executionTime;
        final long tps;
        
        TestResult(double duplicateRate, long executionTime, long tps) {
            this.duplicateRate = duplicateRate;
            this.executionTime = executionTime;
            this.tps = tps;
        }
    }
}
