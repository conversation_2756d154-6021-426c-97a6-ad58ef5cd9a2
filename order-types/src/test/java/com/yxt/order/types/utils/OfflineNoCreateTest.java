package com.yxt.order.types.utils;

import com.yxt.order.types.offline.OfflineUserId;
import org.junit.jupiter.api.Test;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * OfflineNoCreate 并发测试
 * 用于验证优化后的单号生成算法在高并发场景下的重复率
 */
public class OfflineNoCreateTest {

    /**
     * 测试并发场景下的单号重复率
     */
    @Test
    public void testConcurrentNoGeneration() throws InterruptedException {
        int threadCount = 100;  // 线程数
        int operationsPerThread = 1000;  // 每个线程生成的单号数量
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        // 模拟相同的输入参数（这是最容易产生重复的场景）
        String baseId = "1234567890123456789";  // 模拟雪花算法生成的ID
        OfflineUserId userId = OfflineUserId.userId("1001");
        Date date = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 为每次生成使用略微不同的ID（模拟真实场景）
                        String id = baseId + String.format("%03d%03d", threadIndex, j);
                        String no = OfflineNoCreate.create(id, userId, date);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                                System.out.println("发现重复单号: " + no);
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        // 输出测试结果
        System.out.println("=== 并发测试结果 ===");
        System.out.println("总线程数: " + threadCount);
        System.out.println("每线程操作数: " + operationsPerThread);
        System.out.println("总操作数: " + totalOperations);
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.6f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
        System.out.println("平均每秒生成: " + (totalOperations * 1000L / (endTime - startTime)) + " 个单号");
        
        // 显示一些生成的单号样例
        System.out.println("\n=== 生成的单号样例 ===");
        generatedNos.stream().limit(10).forEach(System.out::println);
    }
    
    /**
     * 测试相同输入参数的重复率（最严苛的测试）
     */
    @Test
    public void testSameInputDuplication() throws InterruptedException {
        int threadCount = 50;
        int operationsPerThread = 2000;
        int totalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNos = new HashSet<>();
        AtomicInteger duplicateCount = new AtomicInteger(0);
        Object lock = new Object();
        
        // 使用完全相同的输入参数
        String sameId = "1234567890123456789";
        OfflineUserId sameUserId = OfflineUserId.userId("1001");
        Date sameDate = new Date();
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String no = OfflineNoCreate.create(sameId, sameUserId, sameDate);
                        
                        synchronized (lock) {
                            if (!generatedNos.add(no)) {
                                duplicateCount.incrementAndGet();
                            }
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        latch.await();
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        
        System.out.println("\n=== 相同输入参数测试结果 ===");
        System.out.println("总操作数: " + totalOperations);
        System.out.println("生成的唯一单号数: " + generatedNos.size());
        System.out.println("重复单号数: " + duplicateCount.get());
        System.out.println("重复率: " + String.format("%.6f%%", (double) duplicateCount.get() / totalOperations * 100));
        System.out.println("执行时间: " + (endTime - startTime) + "ms");
    }
    
    /**
     * 测试单号格式的正确性
     */
    @Test
    public void testNoFormat() {
        String id = "1234567890123456789";
        OfflineUserId userId = OfflineUserId.userId("1001");
        Date date = new Date();
        
        // 测试会员单号
        String vipNo = OfflineNoCreate.create(id, userId, date);
        System.out.println("会员单号: " + vipNo);
        System.out.println("单号长度: " + vipNo.length());
        System.out.println("VIP标识: " + vipNo.substring(0, 1));
        
        // 测试非会员单号
        String nonVipNo = OfflineNoCreate.create(id, null, date);
        System.out.println("非会员单号: " + nonVipNo);
        System.out.println("单号长度: " + nonVipNo.length());
        System.out.println("VIP标识: " + nonVipNo.substring(0, 1));
        
        // 测试迁移单号
        String migrateNo = OfflineNoCreate.migrateCreate(id, userId, date);
        System.out.println("迁移单号: " + migrateNo);
        System.out.println("单号长度: " + migrateNo.length());
        System.out.println("VIP标识: " + migrateNo.substring(0, 1));
        
        // 验证单号格式
        ShardingHelper.validNoStr(vipNo);
        ShardingHelper.validNoStr(nonVipNo);
        ShardingHelper.validNoStr(migrateNo);
        
        System.out.println("所有单号格式验证通过！");
    }
}
