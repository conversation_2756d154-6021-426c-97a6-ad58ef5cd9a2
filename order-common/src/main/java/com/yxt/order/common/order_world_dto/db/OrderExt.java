package com.yxt.order.common.order_world_dto.db;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 订单扩展信息
 */
@Getter
@Setter
@ApiModel(description = "订单扩展信息")
public class OrderExt {

  /**
   *
   */
  @ApiModelProperty(value = "超时未支付取消时间")
  private LocalDateTime payTimoutCancelTime;

  @ApiModelProperty(value = "标签数据")
  private List<Tag> tags;

  @Data
  @ApiModel(description = "标签")
  public static class Tag {

    @ApiModelProperty(value = "编码")
    private String code;
  }


}
